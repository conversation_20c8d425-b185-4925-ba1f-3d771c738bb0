import mysql.connector
import sqlite3
from typing import Callable, Any
from mysql.connector import <PERSON>rror
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection


def get_mysql_connection(
    host: str | None = None,
    database: str | None = None,
    user: str | None = None,
    password: str | None = None,
) -> MySQLConnectionAbstract | PooledMySQLConnection | None:
    config = {
        "user": user,
        "password": password,
        "host": host,
        "database": database,
        "raise_on_warnings": True,
    }

    try:
        connection = mysql.connector.connect(**config)
        return connection

    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None


def get_sqlite_connection(database_path: str) -> sqlite3.Connection | None:
    try:
        connection = sqlite3.connect(database_path)
        return connection

    except sqlite3.Error as e:
        print(f"Error connecting to SQLite database: {e}")
        return None


def get_db_connection(
    connection_factory: Callable[..., Any], *args, **kwargs
) -> MySQLConnectionAbstract | PooledMySQLConnection | sqlite3.Connection | None:
    try:
        return connection_factory(*args, **kwargs)
    except Exception as e:
        print(f"Error creating database connection: {e}")
        return None
