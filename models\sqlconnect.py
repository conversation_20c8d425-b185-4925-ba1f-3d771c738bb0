import mysql.connector
import sqlite3
from mysql.connector import <PERSON><PERSON><PERSON>
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection


def get_mysql_connection(
    host: str | None = None,
    database: str | None = None,
    user: str | None = None,
    password: str | None = None,
) -> MySQLConnectionAbstract | PooledMySQLConnection | None:
    config = {
        "user": user,
        "password": password,
        "host": host,
        "database": database,
        "raise_on_warnings": True,
    }

    try:
        connection = mysql.connector.connect(**config)
        return connection

    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None


def get_sqlite_connection(database_path: str) -> sqlite3.Connection | None:
    """
    Create and return a SQLite database connection.

    Args:
        database_path: Path to the SQLite database file

    Returns:
        SQLite connection object or None if connection fails
    """
    try:
        connection = sqlite3.connect(database_path)
        return connection

    except sqlite3.Error as e:
        print(f"Error connecting to SQLite database: {e}")
        return None
