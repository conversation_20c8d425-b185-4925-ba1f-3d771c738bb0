import mysql.connector
from mysql.connector import <PERSON>rror
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection

def get_mysql_connection(
    host: str | None = None,
    database: str | None = None,
    user: str | None = None,
    password: str | None = None
) -> MySQLConnectionAbstract | PooledMySQLConnection | None:

    config = {
        'user': user,
        'password': password,
        'host': host,
        'database': database,
        'raise_on_warnings': True
    }

    try:
        connection = mysql.connector.connect(**config)
        return connection
        
    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None