import mysql.connector
import sqlite3
from typing import Any, Protocol
from mysql.connector import <PERSON>rro<PERSON>
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection


class DatabaseAdapter(Protocol):
    def connect(self, **kwargs) -> Any: ...


def get_mysql_connection(
    host: str | None = None,
    database: str | None = None,
    user: str | None = None,
    password: str | None = None,
) -> MySQLConnectionAbstract | PooledMySQLConnection | None:
    config = {
        "user": user,
        "password": password,
        "host": host,
        "database": database,
        "raise_on_warnings": True,
    }

    try:
        connection = mysql.connector.connect(**config)
        return connection

    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None


def get_sqlite_connection(database_path: str) -> sqlite3.Connection | None:
    try:
        connection = sqlite3.connect(database_path)
        return connection

    except sqlite3.Error as e:
        print(f"Error connecting to SQLite database: {e}")
        return None


class MySQLAdapter:
    def connect(
        self, **kwargs
    ) -> MySQLConnectionAbstract | PooledMySQLConnection | None:
        return get_mysql_connection(**kwargs)


class SQLiteAdapter:
    def connect(self, database_path: str, **kwargs) -> sqlite3.Connection | None:
        return get_sqlite_connection(database_path)


def get_db_connection(
    adapter: DatabaseAdapter, **kwargs
) -> MySQLConnectionAbstract | PooledMySQLConnection | sqlite3.Connection | None:
    try:
        return adapter.connect(**kwargs)
    except Exception as e:
        print(f"Error creating database connection: {e}")
        return None
