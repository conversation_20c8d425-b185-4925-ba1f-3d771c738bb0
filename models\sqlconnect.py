import mysql.connector
import sqlite3
from mysql.connector import <PERSON><PERSON><PERSON>
from mysql.connector.abstracts import MySQLConnectionAbstract
from mysql.connector.pooling import PooledMySQLConnection


def get_mysql_connection(
    host: str | None = None,
    database: str | None = None,
    user: str | None = None,
    password: str | None = None,
) -> MySQLConnectionAbstract | PooledMySQLConnection | None:
    config = {
        "user": user,
        "password": password,
        "host": host,
        "database": database,
        "raise_on_warnings": True,
    }

    try:
        connection = mysql.connector.connect(**config)
        return connection

    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None


def get_sqlite_connection(database_path: str) -> sqlite3.Connection | None:
    try:
        connection = sqlite3.connect(database_path)
        return connection

    except sqlite3.Error as e:
        print(f"Error connecting to SQLite database: {e}")
        return None


def get_db_connection(
    database_path: str | None = None,
    host: str | None = None,
    database: str | None = None,
    user: str | None = None,
    password: str | None = None,
) -> MySQLConnectionAbstract | PooledMySQLConnection | sqlite3.Connection | None:
    """
    Adapter function that returns either MySQL or SQLite connection based on arguments.

    If database_path is provided, returns SQLite connection.
    If host, database, user, password are provided, returns MySQL connection.

    Args:
        database_path: Path to SQLite database file (for SQLite)
        host: MySQL host (for MySQL)
        database: MySQL database name (for MySQL)
        user: MySQL username (for MySQL)
        password: MySQL password (for MySQL)

    Returns:
        Database connection object or None if connection fails
    """
    if database_path is not None:
        # SQLite connection
        return get_sqlite_connection(database_path)
    elif host is not None or database is not None or user is not None or password is not None:
        # MySQL connection
        return get_mysql_connection(host, database, user, password)
    else:
        print("Error: No valid connection parameters provided")
        return None
