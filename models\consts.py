from dotenv import load_dotenv
from enum import StrEnum
import os

load_dotenv()

DB_HOST = os.getenv("DB_HOST")
DB_USER = os.getenv("DB_USER")
DB_PWD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")

TBL_NAME = "products"
DATA_FILE = "table.json"


class Key(StrEnum):
    FROM = "Было"
    TO = "Надо"


class Queries(StrEnum):
    TEST = f"select id, name, vendor from {TBL_NAME} where vendor like '%#%' limit 5;"
    SWAP_VENDORS = f"update {TBL_NAME} set vendor = %s where vendor = %s;"
    TRIM_VENDORS = f"update {TBL_NAME} set vendor = SUBSTRING_INDEX(vendor, '#', 1) where vendor like '%#%';"

