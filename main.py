from models.reader import load_json_to_dict
from models.sqlconnect import get_mysql_connection
from models.consts import (
    DB_HOST,
    DB_USER,
    DB_PWD,
    DB_NAME,
    TBL_NAME,
    DATA_FILE,
    Key,
)


def main():
    data = load_json_to_dict(DATA_FILE)

    for el in data:
        print(f"{el[Key.FROM]} :  {el[Key.TO]}")

    conn = get_mysql_connection(DB_HOST, DB_NAME, DB_USER, DB_PWD)
    cursor = conn.cursor()  # Create a cursor object
    query = f"select id, name, vendor from {TBL_NAME} where vendor like '%#%' limit 5;"
    cursor.execute(query)
    results = cursor.fetchall()

    print(results)

    conn.close()


if __name__ == "__main__":
    main()
