import argparse
from models.reader import load_json_to_dict
from models.sqlconnect import get_db_connection, MySQLAdapter
from models.consts import DB_HOST, DB_USER, DB_PWD, DB_NAME, DATA_FILE, Key, Queries


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "mode",
        nargs="?",
        choices=["test", "swap_vendors", "trim_vendors"],
        default="test",
        help="Execution mode (default: test)"
    )

    args = parser.parse_args()
    query = getattr(Queries, args.mode.upper())

    mysql_adapter = MySQLAdapter()

    conn = get_db_connection(
        mysql_adapter, host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PWD
    )
    
    if not conn:
        print("Failed to connect to database. Exiting...")
        return

    cursor = conn.cursor()

    data = load_json_to_dict(DATA_FILE)

    

    match query:
        case Queries.TEST:
            cursor.execute(query)
            results = cursor.fetchall()
        case Queries.SWAP_VENDORS:
            for el in data:
                cursor.execute(query, (el[Key.TO], el[Key.FROM]))
            conn.commit()
            results = f"Updated {cursor.rowcount} rows"
        case Queries.TRIM_VENDORS:
            cursor.execute(query)
            conn.commit()
            results = f"Updated {cursor.rowcount} rows"
        case _:
            results = "Unknown query type"

    print(f"Query: {query}")
    print(f"Results: {results}")

    conn.close()


if __name__ == "__main__":
    main()
