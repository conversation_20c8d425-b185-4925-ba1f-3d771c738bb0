from dotenv import load_dotenv
import os

from models.reader import load_json_to_dict
from models.sqlconnect import get_mysql_connection

load_dotenv()

DB_HOST = os.getenv("DB_HOST")
DB_USER = os.getenv("DB_USER")
DB_PWD = os.getenv("DB_PWD")
DB_NAME = os.getenv("DB_NAME")

def main():

    csv_file = 'table.json'
 
    data = load_json_to_dict(csv_file)
    
#    for el in data:
#        print(f"{el['Было']} :  {el['Надо']}")
    
    conn = get_mysql_connection(DB_HOST, DB_NAME, DB_USER, DB_PWD)
    cursor = conn.cursor()     # Create a cursor object
    cursor.execute('select id, name, vendor from products where vendor like "%#%" limit 5;')
    results = cursor.fetchall()

    print(results)

    conn.close()

if __name__ == "__main__":
    main()