import argparse
from models.reader import load_json_to_dict
from models.sqlconnect import get_db_connection, MySQLAdapter
from models.consts import DB_HOST, DB_USER, DB_PWD, DB_NAME, DATA_FILE, Key, Queries


def main():
    parser = argparse.ArgumentParser(description="Run database queries")
    parser.add_argument(
        "--query",
        choices=["TEST", "SWAP_VENDORS", "TRIM_VENDORS"],
        default="TEST",
        help="Query to execute (default: TEST)"
    )
    parser.add_argument(
        "--custom-query",
        type=str,
        help="Custom SQL query to execute (overrides --query)"
    )

    args = parser.parse_args()

    if args.custom_query:
        query = args.custom_query
    else:
        query = getattr(Queries, args.query)
    data = load_json_to_dict(DATA_FILE)

    mysql_adapter = MySQLAdapter()

    conn = get_db_connection(
        mysql_adapter, host=DB_HOST, database=DB_NAME, user=DB_USER, password=DB_PWD
    )
    cursor = conn.cursor()

    if args.custom_query:
        # Handle custom query
        cursor.execute(query)
        if query.strip().upper().startswith(('SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN')):
            results = cursor.fetchall()
        else:
            conn.commit()
            results = f"Query executed, {cursor.rowcount} rows affected"
    else:
        # Handle predefined queries
        match query:
            case Queries.TEST:
                cursor.execute(query)
                results = cursor.fetchall()
            case Queries.SWAP_VENDORS:
                for el in data:
                    cursor.execute(query, (el[Key.TO], el[Key.FROM]))
                conn.commit()
                results = f"Updated {cursor.rowcount} rows"
            case Queries.TRIM_VENDORS:
                cursor.execute(query)
                conn.commit()
                results = f"Updated {cursor.rowcount} rows"

    print(f"Query: {query}")
    print(f"Results: {results}")

    conn.close()


if __name__ == "__main__":
    main()
